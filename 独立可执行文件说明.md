# CCTS 独立可执行文件说明

## 生成的文件
- **文件名**: `publish/CCTS.exe`
- **文件大小**: 约 77 MB
- **目标平台**: Windows x64 (Windows 10/11)
- **框架**: .NET 8.0 (自包含)

## 主要特性
✅ **完全独立**: 包含完整的 .NET 8.0 运行时  
✅ **单文件部署**: 所有依赖项都打包在一个 .exe 文件中  
✅ **无需安装**: 目标电脑无需安装 .NET 运行时  
✅ **即插即用**: 复制到任何 Windows 电脑即可运行  

## 技术配置

### 项目文件更新
已将项目从 .NET 9.0 降级到 .NET 8.0 以提高兼容性，并添加了以下发布配置：

```xml
<!-- 发布配置 -->
<PublishSingleFile>true</PublishSingleFile>
<SelfContained>true</SelfContained>
<RuntimeIdentifier>win-x64</RuntimeIdentifier>
<IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
<IncludeAllContentForSelfExtract>true</IncludeAllContentForSelfExtract>
<EnableCompressionInSingleFile>true</EnableCompressionInSingleFile>

<!-- 优化设置 -->
<PublishTrimmed>false</PublishTrimmed>
<PublishReadyToRun>true</PublishReadyToRun>
<TieredCompilation>true</TieredCompilation>
<TieredPGO>true</TieredPGO>
```

### 发布命令
```bash
dotnet publish -c Release -r win-x64 --self-contained true \
  -p:PublishSingleFile=true \
  -p:IncludeNativeLibrariesForSelfExtract=true \
  -p:IncludeAllContentForSelfExtract=true \
  -p:EnableCompressionInSingleFile=true \
  -p:PublishTrimmed=false \
  -p:PublishReadyToRun=true \
  -p:DebugType=None \
  -p:DebugSymbols=false \
  -p:UseAppHost=true \
  -o "../publish"
```

## 部署说明

### 在其他电脑上运行
1. 将 `publish/CCTS.exe` 复制到目标电脑
2. 双击运行，无需任何额外安装
3. 首次运行可能需要几秒钟解压内嵌的运行时

### 系统要求
- **操作系统**: Windows 10 版本 1607 或更高版本 / Windows 11
- **架构**: x64 (64位)
- **内存**: 建议 4GB 以上
- **磁盘空间**: 运行时需要约 200MB 临时空间用于解压

### 防病毒软件
某些防病毒软件可能会误报自包含的 .exe 文件。如果遇到此问题：
1. 将文件添加到防病毒软件的白名单
2. 或者临时禁用实时保护进行首次运行

## 故障排除

### 如果仍然无法运行
1. **检查目标系统**: 确保是 Windows 10/11 x64
2. **管理员权限**: 尝试以管理员身份运行
3. **Windows Defender**: 检查是否被 Windows Defender 阻止
4. **事件查看器**: 查看 Windows 事件日志中的错误信息

### 重新生成
如果需要重新生成，运行以下命令：
```bash
cd CCTS
dotnet clean -c Release
dotnet restore
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true -p:IncludeAllContentForSelfExtract=true -p:EnableCompressionInSingleFile=true -p:PublishTrimmed=false -p:PublishReadyToRun=true -p:DebugType=None -p:DebugSymbols=false -p:UseAppHost=true -o "../publish"
```

## 与之前版本的区别

### 主要改进
1. **框架版本**: 从 .NET 9.0 降级到 .NET 8.0 (更稳定，兼容性更好)
2. **完整包含**: 禁用了代码裁剪 (`PublishTrimmed=false`) 以避免运行时错误
3. **内容包含**: 确保所有内容都包含在单文件中
4. **压缩优化**: 启用压缩以减小文件大小
5. **性能优化**: 启用 ReadyToRun 和 PGO 优化

### 为什么之前的版本可能失败
1. **.NET 9.0 太新**: 可能在某些系统上不兼容
2. **代码裁剪**: 可能意外移除了必要的代码
3. **依赖项缺失**: 某些原生库可能没有正确包含

现在生成的 `CCTS.exe` 应该可以在任何 Windows 10/11 x64 系统上直接运行，无需安装任何额外的运行时环境。
