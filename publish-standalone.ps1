# PowerShell 发布脚本
Write-Host "正在创建独立可执行文件..." -ForegroundColor Green
Write-Host ""

# 清理之前的发布文件
if (Test-Path "publish") {
    Remove-Item -Recurse -Force "publish"
    Write-Host "已清理旧的发布文件" -ForegroundColor Yellow
}

# 进入项目目录
Set-Location "CCTS"

try {
    Write-Host "步骤 1: 清理项目..." -ForegroundColor Cyan
    dotnet clean -c Release
    if ($LASTEXITCODE -ne 0) { throw "清理失败" }

    Write-Host "步骤 2: 还原包..." -ForegroundColor Cyan
    dotnet restore
    if ($LASTEXITCODE -ne 0) { throw "包还原失败" }

    Write-Host "步骤 3: 发布独立可执行文件..." -ForegroundColor Cyan
    
    # 使用更兼容的发布参数
    $publishArgs = @(
        "publish"
        "-c", "Release"
        "-r", "win-x64"
        "--self-contained", "true"
        "-p:PublishSingleFile=true"
        "-p:IncludeNativeLibrariesForSelfExtract=true"
        "-p:IncludeAllContentForSelfExtract=true"
        "-p:EnableCompressionInSingleFile=true"
        "-p:PublishTrimmed=false"
        "-p:PublishReadyToRun=true"
        "-p:DebugType=None"
        "-p:DebugSymbols=false"
        "-p:UseAppHost=true"
        "-o", "../publish"
    )
    
    & dotnet $publishArgs
    if ($LASTEXITCODE -ne 0) { throw "发布失败" }

    Write-Host ""
    Write-Host "✓ 发布成功！" -ForegroundColor Green
    Write-Host "可执行文件位置: $(Resolve-Path '../publish/CCTS.exe')" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "该文件可以在任何Windows 10/11电脑上直接运行，无需安装.NET运行时" -ForegroundColor Green
    
    # 显示文件大小
    $exeFile = Get-Item "../publish/CCTS.exe"
    $sizeInMB = [math]::Round($exeFile.Length / 1MB, 2)
    Write-Host "文件大小: $sizeInMB MB" -ForegroundColor Yellow
    
} catch {
    Write-Host ""
    Write-Host "✗ 发布失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请检查错误信息并重试" -ForegroundColor Red
} finally {
    Set-Location ".."
    Write-Host ""
    Read-Host "按任意键继续..."
}
