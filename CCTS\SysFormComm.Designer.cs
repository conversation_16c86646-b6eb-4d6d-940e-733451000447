﻿using System.Windows.Forms.VisualStyles;

namespace CCTS;

partial class SysFormComm

{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }


    #region Windows Form Designer generated code
    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// 2025-06-05
    /// </summary>
    private void InitializeComponent()
    {

        this.components = new System.ComponentModel.Container();
        this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
        this.ClientSize = new System.Drawing.Size(400, 500);
        this.Text = "Comm";
        this.Icon = new System.Drawing.Icon(@"C:\work\C#\Tecumseh\CCTS\CCTS.ico");
        this.StartPosition = FormStartPosition.CenterScreen;
        this.BackColor = Color.FromArgb(78, 149, 217);
        //
        //lable1
        //
        this.label1 = new Label();
        this.label1.Text = "Comm:";
        this.label1.Size = new Size(110, 40);
        this.label1.BackColor = Color.FromArgb(78, 149, 217);
        this.label1.ForeColor = Color.White;
        this.label1.Font = new Font(this.label1.Font.FontFamily, 10);
        this.label1.Location = new Point(40, 40);
        this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
        this.Controls.Add(this.label1);
        //
        //combo_comm
        //
        this.combo_comm = new ComboBox();
        this.combo_comm.Location = new Point(label1.Width + 40, 40);
        this.combo_comm.Size = new Size(200, 80);
        this.combo_comm.BackColor = Color.White;
        this.combo_comm.ForeColor = Color.Black;
        this.combo_comm.Font = new Font(this.combo_comm.Font.FontFamily, 10);
        this.Controls.Add(this.combo_comm);
        //
        //label2
        //
        this.label2 = new Label();
        this.label2.Text = "Bps:";
        this.label2.Size = new Size(110, 40);
        this.label2.BackColor = Color.FromArgb(78, 149, 217);
        this.label2.ForeColor = Color.White;
        this.label2.Font = new Font(this.label2.Font.FontFamily, 10);
        this.label2.Location = new Point(40, this.label1.Height + 50);
        this.label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
        this.Controls.Add(this.label2);
        //
        //combo_bps
        //
        this.combo_bps = new ComboBox();
        this.combo_bps.Location = new Point(label2.Width + 40, this.label1.Height + 50);
        this.combo_bps.Size = new Size(200, 80);
        this.combo_bps.BackColor = Color.White;
        this.combo_bps.ForeColor = Color.Black;
        this.combo_bps.Font = new Font(this.combo_bps.Font.FontFamily, 10);
        this.Controls.Add(this.combo_bps);
        //
        //label3
        //
        this.label3 = new Label();
        this.label3.Text = "Parity:";
        this.label3.Size = new Size(110, 40);
        this.label3.BackColor = Color.FromArgb(78, 149, 217);
        this.label3.ForeColor = Color.White;
        this.label3.Font = new Font(this.label3.Font.FontFamily, 10);
        this.label3.Location = new Point(40, this.label1.Height + this.label2.Height + 60);
        this.label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
        this.Controls.Add(this.label3);
        //
        //combo_parity
        //
        this.combo_parity = new ComboBox();
        this.combo_parity.Location = new Point(label3.Width + 40, this.label1.Height + this.label2.Height + 60);
        this.combo_parity.Size = new Size(200, 80);
        this.combo_parity.BackColor = Color.White;
        this.combo_parity.ForeColor = Color.Black;
        this.combo_parity.Font = new Font(this.combo_parity.Font.FontFamily, 10);
        this.Controls.Add(this.combo_parity);
        //
        //label4
        //
        this.label4 = new Label();
        this.label4.Text = "Data Bit:";
        this.label4.Size = new Size(110, 40);
        this.label4.BackColor = Color.FromArgb(78, 149, 217);
        this.label4.ForeColor = Color.White;
        this.label4.Font = new Font(this.label4.Font.FontFamily, 10);
        this.label4.Location = new Point(40, this.label1.Height + this.label2.Height + this.label3.Height + 70);
        this.label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
        this.Controls.Add(this.label4);
        //
        //combo_databit
        //
        this.combo_databit = new ComboBox();
        this.combo_databit.Location = new Point(label4.Width + 40, this.label1.Height + this.label2.Height + this.label3.Height + 70);
        this.combo_databit.Size = new Size(200, 80);
        this.combo_databit.BackColor = Color.White;
        this.combo_databit.ForeColor = Color.Black;
        this.combo_databit.Font = new Font(this.combo_databit.Font.FontFamily, 10);
        this.Controls.Add(this.combo_databit);
        //
        //label5
        //
        this.label5 = new Label();
        this.label5.Text = "Stop Bit:";
        this.label5.Size = new Size(110, 40);
        this.label5.BackColor = Color.FromArgb(78, 149, 217);
        this.label5.ForeColor = Color.White;
        this.label5.Font = new Font(this.label5.Font.FontFamily, 10);
        this.label5.Location = new Point(40, this.label1.Height + this.label2.Height + this.label3.Height + label4.Height + 80);
        this.label5.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
        this.Controls.Add(this.label5);
        //
        //combo_stopbit
        //
        this.combo_stopbit = new ComboBox();
        this.combo_stopbit.Location = new Point(label5.Width + 40, this.label1.Height + this.label2.Height + this.label3.Height + label4.Height + 80);
        this.combo_stopbit.Size = new Size(200, 80);
        this.combo_stopbit.BackColor = Color.White;
        this.combo_stopbit.ForeColor = Color.Black;
        this.combo_stopbit.Font = new Font(this.combo_stopbit.Font.FontFamily, 10);               
        this.combo_stopbit.DropDownStyle = ComboBoxStyle.DropDown;
        this.Controls.Add(this.combo_stopbit);
        //
        //btn_connect
        //
        this.btn_connect = new CCTS.Roundcornerbtn();
        this.btn_connect.Size = new Size(150, 60);
        this.btn_connect.Text = "Connect";
        this.btn_connect.Font = new Font(this.btn_connect.Font.FontFamily, 10, FontStyle.Bold);
        this.btn_connect.Location = new Point(40, 340);       
        this.btn_connect.ForeColor = Color.White;        
        this.btn_connect.BackColor = Color.FromArgb(192, 79, 21);
        this.btn_connect.FlatStyle = FlatStyle.Flat;
        this.btn_connect.FlatAppearance.BorderSize = 0;
        this.btn_connect.Click += new System.EventHandler(this.btn_connect_Click);
        this.Controls.Add(btn_connect);
        //
        //btn_save
        //
        this.btn_save = new CCTS.Roundcornerbtn();
        this.btn_save.Size = new Size(150, 60);
        this.btn_save.Text = "Save";
        this.btn_save.Font = new Font(this.btn_connect.Font.FontFamily, 10, FontStyle.Bold);
        this.btn_save.Location = new Point(220, 340);
        this.btn_save.ForeColor = Color.White;       
        this.btn_save.BackColor = Color.FromArgb(11, 48, 65);
        this.btn_save.FlatStyle = FlatStyle.Flat;
        this.btn_save.FlatAppearance.BorderSize = 0;
        this.btn_save.Click += new System.EventHandler(this.btn_save_Click);
        this.Controls.Add(btn_save);


       
       


    }

    #endregion
    private System.Windows.Forms.Label label1, label2, label3,label4, label5;
    private System.Windows.Forms.ComboBox combo_bps, combo_comm, combo_parity, combo_databit, combo_stopbit;

    private System.Windows.Forms.Button btn_connect, btn_save;
}

