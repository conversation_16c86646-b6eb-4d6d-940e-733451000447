@echo off
echo 正在创建独立可执行文件...
echo.

REM 清理之前的发布文件
if exist "publish" rmdir /s /q "publish"

REM 进入项目目录
cd CCTS

echo 步骤 1: 清理项目...
dotnet clean -c Release

echo 步骤 2: 还原包...
dotnet restore

echo 步骤 3: 发布独立可执行文件...
dotnet publish -c Release -r win-x64 --self-contained true ^
  -p:PublishSingleFile=true ^
  -p:IncludeNativeLibrariesForSelfExtract=true ^
  -p:IncludeAllContentForSelfExtract=true ^
  -p:EnableCompressionInSingleFile=true ^
  -p:PublishTrimmed=false ^
  -p:PublishReadyToRun=true ^
  -p:DebugType=None ^
  -p:DebugSymbols=false ^
  -o "../publish"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ 发布成功！
    echo 可执行文件位置: %~dp0publish\CCTS.exe
    echo.
    echo 该文件可以在任何Windows 10/11电脑上直接运行，无需安装.NET运行时
    echo.
    pause
) else (
    echo.
    echo ✗ 发布失败，请检查错误信息
    echo.
    pause
)
