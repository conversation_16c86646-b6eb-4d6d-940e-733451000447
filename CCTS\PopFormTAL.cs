namespace CCTS;

public partial class PopFormTAL : Form
{
    private MainForm mainForm;
    public DoubleBufferedDataGridView dgTALStep = new DoubleBufferedDataGridView();
    public DoubleBufferedDataGridView dgTALParameters = new DoubleBufferedDataGridView();
    public string[][] colText_talParameters =
    {
        new string[] {"TAL Speed Max.", "TAL Speed Min.", "TAL Sensitivity", "TAL Recovery Speed"},
        new string[] {"RPM  (Compressor maximum speed allowed)",  "RPM  (Compressor minimum speed allowed)",
            "(TAL algorithm sensitivity(0.5 - 1.5))", "(TAL recovery time(15 - 25min))"
        }
    };
    public string[][] colText_talStep = {
        new string[] {"", "Time(min)","RPM"},
        new string[]{ "Step 1:", "Step 2:", "Step 3:"}
    };
    int[] colWidth_dgtalStep = { 100, 240, 240 };
    int[] colWidth_dgtalParameters = { 260, 240, 530 };
    short index;
    public PopFormTAL(MainForm mainForm)
    {
        InitializeComponent();
        this.mainForm = mainForm;
        setGrid0();
        setGrid1();


    }

    private void setGrid0()
    {
        //设置grid样式
        dgTALStep.AllowUserToAddRows = false;
        dgTALStep.AllowUserToOrderColumns = false;
        dgTALStep.AllowUserToDeleteRows = false;
        dgTALStep.AllowUserToResizeRows = false;
        dgTALStep.AllowUserToResizeColumns = false;
        dgTALStep.EnableHeadersVisualStyles = false;
        dgTALStep.RowHeadersVisible = false;
        dgTALStep.BorderStyle = BorderStyle.None;
        dgTALStep.RowHeadersVisible = false;

        //  设置列头
        dgTALStep.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
        dgTALStep.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(11, 48, 65);
        dgTALStep.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
        dgTALStep.ColumnHeadersDefaultCellStyle.Font = new Font(this.panel_talStep.Font.FontFamily, 10, FontStyle.Bold);
        dgTALStep.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        dgTALStep.ColumnHeadersHeight = 70;
        dgTALStep.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;


        dgTALStep.BackgroundColor = Color.FromArgb(11, 48, 65);
        dgTALStep.Font = new Font(dgTALStep.Font.FontFamily, 10);
        dgTALStep.Height = 320;
        dgTALStep.Width = 700;
        dgTALStep.Location = new Point(40, label2.Height + 20);
        dgTALStep.DefaultCellStyle.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
        dgTALStep.GridColor = Color.FromArgb(11, 48, 65);
        panel_talStep.Controls.Add(dgTALStep);


        dgTALStep.CellValidating += (sender, e) =>
       {
           // 只处理第1列和第2列（可编辑列）
           if (e.ColumnIndex < 1 || e.ColumnIndex > 2) return;

           // 获取当前行
           int row = e.RowIndex;

           // 检查输入是否为数字
           if (!float.TryParse(e.FormattedValue.ToString(), out float value))
           {
               //MessageBox.Show("Please enter the number!");
               //e.Cancel = true;
               //return;
               dgTALStep.BeginEdit(true);
           }

           // === 第1列验证 (所有行范围5~20) ===
           if (e.ColumnIndex == 1)
           {
               if (value < 5 || value > 20)
               {
                   MessageBox.Show("Value must be between5.0 and 20.0.");
                   //e.Cancel = true;
                   dgTALStep.BeginEdit(true);
               }
           }
           // === 第2列验证 (各行不同范围) ===
           else if (e.ColumnIndex == 2)
           {
               string errorMsg = "";
               bool valid = true;

               switch (row)
               {
                   case 0: // 第1行
                       valid = value >= 1600 && value <= 3000;
                       errorMsg = "Value must be between 1600.0 and 3000.0.";
                       break;
                   case 1: // 第2行
                   case 2: // 第3行
                       valid = value >= 2500 && value <= 4500;
                       errorMsg = "Value must be between 2500.0 and 4500.0.";
                       break;
                   default:
                       break;
               }

               if (!valid)
               {
                   MessageBox.Show(errorMsg);
                   //    e.Cancel = true;
                   //    return;
                   dgTALStep.BeginEdit(true);
               }

               // === RPM序列规则验证 ===
               float rpm1, rpm2, rpm3;
               if (dgTALStep.Rows[0].Cells[2].Value != null && dgTALStep.Rows[1].Cells[2].Value != null && dgTALStep.Rows[2].Cells[2].Value != null)
               {
                   // 获取当前各行的RPM值
                   //if (dgTALStep.Rows[0].Cells[2].Value != null)
                   float.TryParse(dgTALStep.Rows[0].Cells[2].Value.ToString(), out rpm1);
                   //if (dgTALStep.Rows[1].Cells[2].Value != null)
                   float.TryParse(dgTALStep.Rows[1].Cells[2].Value.ToString(), out rpm2);
                   //if (dgTALStep.Rows[2].Cells[2].Value != null)
                   float.TryParse(dgTALStep.Rows[2].Cells[2].Value.ToString(), out rpm3);

                   // 如果是当前正在编辑的单元格，使用新值
                   if (row == 0) rpm1 = value;
                   else if (row == 1) rpm2 = value;
                   else if (row == 2) rpm3 = value;

                   // 检查序列规则：Step1 RPM ≤ Step2 RPM ≤ Step3 RPM
                   if (rpm1 > rpm2 || rpm2 > rpm3)
                   {
                       MessageBox.Show("Must be satisfied: Step1 RPM ≤ Step2 RPM ≤ Step3 RPM");
                       dgTALStep.BeginEdit(true);
                       //e.Cancel = true;
                   }
               }              
           }
       };

        //添加列
        for (int i = 0; i < colText_talStep[0].Length; i++)
        {
            DataGridViewColumn customColumn;
            customColumn = new DataGridViewTextBoxColumn();
            customColumn.Width = colWidth_dgtalStep[i];
            customColumn.HeaderText = colText_talStep[0][i];
            if (i == 1 || i == 2)
                customColumn.ValueType = typeof(float);

            dgTALStep.Columns.Add(customColumn);
        }
        //添加行
        for (int i = 0; i < colText_talStep[1].Length; i++)
        {
            // 添加新行
            int rowIndex = dgTALStep.Rows.Add();
            dgTALStep.Rows[rowIndex].Cells[0].Value = colText_talStep[1][i];
            //dgParam.Rows[rowIndex].Cells[2].Value = colText_Param[1][i];
            dgTALStep.Rows[rowIndex].Height = 70;
            dgTALStep.Rows[rowIndex].Cells[1].Style.BackColor = Color.White;
            dgTALStep.Rows[rowIndex].Cells[1].Style.ForeColor = Color.Black;
            dgTALStep.Rows[rowIndex].Cells[2].Style.BackColor = Color.White;
            dgTALStep.Rows[rowIndex].Cells[2].Style.ForeColor = Color.Black;
            //dgParam.Rows[rowIndex].Cells[1].Style.Padding = new Padding(0, 10, 0, 10);

            dgTALStep.Rows[i].DefaultCellStyle.BackColor = Color.FromArgb(11, 48, 65);
            //行间距
            dgTALStep.Rows[rowIndex].DividerHeight = 15;
            //列间距 
            dgTALStep.Columns[1].DividerWidth = 40;

            // string tip = "";
            // switch (i)
            // {
            //     case 0: // 第1行
            //         tip = "允许范围: 1600~3000";
            //         break;
            //     case 1: // 第2行
            //     case 2: // 第3行
            //         tip = "允许范围: 2500~4500";
            //         break;
            // }

            // // 为第1列添加范围提示
            // dgTALStep.Rows[i].Cells[1].ToolTipText = "允许范围: 5~20";

            // // 为第2列添加范围提示
            // if (!string.IsNullOrEmpty(tip))
            // {
            //     dgTALStep.Rows[i].Cells[2].ToolTipText = tip;
            //     dgTALStep.Rows[i].Cells[0].Value = $"{colText_talStep[1][i]}";
            // }
        }
    }
    private void setGrid1()
    {
        //设置grid样式
        dgTALParameters.AllowUserToAddRows = false;
        dgTALParameters.AllowUserToOrderColumns = false;
        dgTALParameters.AllowUserToDeleteRows = false;
        dgTALParameters.AllowUserToResizeRows = false;
        dgTALParameters.AllowUserToResizeColumns = false;
        //dgParamRight.EnableHeadersVisualStyles = false;
        dgTALParameters.RowHeadersVisible = false;
        dgTALParameters.BorderStyle = BorderStyle.None;
        dgTALParameters.RowHeadersVisible = false;
        dgTALParameters.ColumnHeadersVisible = false;



        dgTALParameters.BackgroundColor = Color.FromArgb(11, 48, 65);
        dgTALParameters.GridColor = Color.FromArgb(11, 48, 65);
        dgTALParameters.Font = new Font(dgTALParameters.Font.FontFamily, 10);
        dgTALParameters.Height = 320;
        dgTALParameters.Width = 1050;
        dgTALParameters.Location = new Point(10, label3.Height + 30);
        dgTALParameters.DefaultCellStyle.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
        panel_talParameters.Controls.Add(dgTALParameters);

    //     dgTALParameters.CellValidating += (sender, e) =>
    //    {
    //        // 只处理第2列（可编辑列）
    //        if (e.ColumnIndex != 1) return;

    //        // 获取当前行
    //        int row = e.RowIndex;
    //        if (row > 1) return;

    //        // 检查输入是否为数字
    //        if (!float.TryParse(e.FormattedValue.ToString(), out float value))
    //        {
    //            //MessageBox.Show("Please enter the number!");
    //            //e.Cancel = true; // 阻止离开单元格
    //            //return;
    //             dgTALParameters.BeginEdit(true);
    //        }

    //        //检查每行的数值范围 ---
    //        bool valid = false;
    //        string errorMsg = "";

    //        // 按行判断范围
    //        if (row == 0)
    //        {
    //            valid = (value >= 3000.0 && value <= 4500.0);
    //            errorMsg = "Value must be between 3000.0 and 4500.0.";
    //        }
    //        else if (row == 1)
    //        {
    //            valid = (value >= 1600.0 && value <= 4500.0);
    //            errorMsg = "Value must be between 1600.0 and 4500.0";
    //        }


    //        // 显示错误提示
    //        if (!valid)
    //        {
    //            MessageBox.Show(errorMsg);
    //            //    e.Cancel = true;
    //            //    return;
    //         dgTALParameters.BeginEdit(true);
    //        }

    //        // 检查特殊规则 
    //        if (row == 0 || row == 1 || row == 2 || row == 3)
    //        {
    //            if (dgTALParameters.Rows[0].Cells[1].Value != null && dgTALParameters.Rows[1].Cells[1].Value != null)
    //            {
    //                float maxSpeed = (row == 0) ?
    //                             float.Parse(e.FormattedValue.ToString()) :
    //                             float.Parse(dgTALParameters.Rows[0].Cells[1].Value.ToString());
    //                float minSpeed = (row == 1) ?
    //                             float.Parse(e.FormattedValue.ToString()) :
    //                             float.Parse(dgTALParameters.Rows[1].Cells[1].Value.ToString());

    //                if (maxSpeed < minSpeed)
    //                {
    //                    MessageBox.Show("Maximum speed must be ≥ minimum speed");
    //                    e.Cancel = true;
    //                }
    //            }
    //        }
        //    if (e.ColumnIndex == 1)
               //    {
               //        // 强制提交当前编辑
               //        dgTALParameters.CommitEdit(DataGridViewDataErrorContexts.Commit);
               //    }
        //    };

        for (int i = 0; i < 3; i++)
        {
            DataGridViewColumn customColumn;
            customColumn = new DataGridViewTextBoxColumn();
            customColumn.Width = colWidth_dgtalParameters[i];
            if (i == 0 || i == 2)
            {
                customColumn.ReadOnly = true;
                customColumn.DefaultCellStyle.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            }
            else if (i == 1)
                customColumn.ValueType = typeof(float); 

            dgTALParameters.Columns.Add(customColumn);
        }
        //添加行
        for (int i = 0; i < colText_talParameters[0].Length; i++)
        {
            // 添加新行
            int rowIndex = dgTALParameters.Rows.Add();
            dgTALParameters.Rows[rowIndex].Cells[0].Value = colText_talParameters[0][i];
            dgTALParameters.Rows[rowIndex].Cells[2].Value = colText_talParameters[1][i];
            dgTALParameters.Rows[rowIndex].Height = 70;
            dgTALParameters.Rows[rowIndex].Cells[1].Style.BackColor = Color.White;
            dgTALParameters.Rows[rowIndex].Cells[1].Style.ForeColor = Color.Black;
            //dgParam.Rows[rowIndex].Cells[1].Style.Padding = new Padding(0, 10, 0, 10);

            dgTALParameters.Rows[i].DefaultCellStyle.BackColor = Color.FromArgb(11, 48, 65);
            dgTALParameters.Rows[rowIndex].DividerHeight = 15;

        }
    }
    //TAL First Power On Step参数读写
    private void btn_readStep_Click(object sender, EventArgs e)
    {
        Task.Run(() =>
       {
           for (int i = 14; i < 20; i++)
           {
               //float val = 0;
               index = (short)i;
               bool res = ProtocalCmd.ReadParam(index, SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, 1);
               if (res)
               {
                   dgTALStep.Invoke((MethodInvoker)delegate
                   {
                       int rowIndex = (i - 14) / 2;  // 14/15->0, 16/17->1, 18/19->2
                                                     // 计算列索引：偶数为第一列, 奇数为第二列
                       int colIndex = i % 2 == 0 ? 1 : 2;

                       dgTALStep.Rows[rowIndex].Cells[colIndex].Value = FormatFloatValue(ProtocalCmd.val);                  

                   });
               }

           }
       });

    }
    private void btn_writeStep_Click(Object sender, EventArgs e)
    {
        Task.Run(() =>
        {
            if (dgTALStep == null || dgTALStep == null || dgTALStep.Rows.Count == 0 || dgTALStep.Rows.Count == 0)
            {
                MainForm.Logger.Warn("数据表格未初始化或位空");
                return;
            }
            for (int i = 14; i < 20; i++)
            {
                index = (short)i;

                // 统一计算行索引和列索引
                int rowIndex = (i - 14) / 2;  // 14/15→0, 16/17→1, 18/19→2
                int colIndex = (i % 2 == 0) ? 1 : 2; // 偶数列1(索引1)，奇数列2(索引2)

                // 检查行索引范围
                if (rowIndex < 0 || rowIndex >= dgTALStep.Rows.Count)
                {
                    MainForm.Logger.Error($"无效的行索引：{rowIndex}");
                    continue;
                }

                // 获取单元格值
                var cell = dgTALStep.Rows[rowIndex].Cells[colIndex];
                if (cell.Value == null)
                {
                    MainForm.Logger.Warn($"行{rowIndex}列{colIndex}数据为空");
                    continue;
                }

                // 解析数值
                if (!float.TryParse(cell.Value.ToString(), out float value))
                {
                    MainForm.Logger.Error($"行{rowIndex}数据转换失败：{cell.Value}");
                    continue;
                }

                // 写入操作
                bool res = ProtocalCmd.WriteParam(index, value, SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, 1);

                if (res)
                {
                    MainForm.Logger.Info($"成功写入：行{rowIndex}列{colIndex} → 索引={index}, 值={value}");
                    bool resSave = ProtocalCmd.SaveParam(SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, 1);
                    if (resSave)
                        MainForm.Logger.Info("保存指令发送成功");
                    else
                        MainForm.Logger.Info("保存指令发送失败");
                }
                else
                {
                    MainForm.Logger.Error($"写入失败：行{rowIndex}列{colIndex} → 索引={index}, 值={value}");
                }

            }
        });
    }
    //TAL Algorithm Parameters参数读写
    private void btn_readParameters_Click(object sender, EventArgs e)
    {
        Task.Run(() =>
       {
           for (int i = 20; i < 24; i++)
           {
               //float val = 0;
               index = (short)i;
               bool res = ProtocalCmd.ReadParam(index, SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, 1);
               if (res)
               {
                   dgTALParameters.Invoke((MethodInvoker)delegate
                   {
                       dgTALParameters.Rows[i - 20].Cells[1].Value = FormatFloatValue(ProtocalCmd.val);
                   });
               }

           }
       });
        

    }
    private void btn_writeParameters_Click(Object sender, EventArgs e)
    {
        // dgTALParameters.EndEdit(); // 强制提交所有编辑中的单元格    
        // float maxSpeed = float.Parse(dgTALParameters.Rows[0].Cells[1].Value.ToString());
        // float minSpeed = float.Parse(dgTALParameters.Rows[1].Cells[1].Value.ToString());
        // if (maxSpeed < minSpeed)
        // {
        //     MessageBox.Show("Maximum speed must be ≥ minimum speed");
        //     return;
        // }

        Task.Run(() =>
        {
            if (dgTALParameters == null || dgTALParameters == null || dgTALParameters.Rows.Count == 0 || dgTALParameters.Rows.Count == 0)
            {
                MainForm.Logger.Warn("数据表格未初始化或位空");
                return;
            }
            for (int i = 20; i < 24; i++)
            {
                index = (short)i;
                float value = 0;

                bool parseSuccess = float.TryParse(dgTALParameters.Rows[i - 20].Cells[1].Value.ToString(), out value);
                if (!parseSuccess)
                {
                    MainForm.Logger.Error($"第{i - 20}行数据转换失败：{dgTALParameters.Rows[i - 20].Cells[1].Value}");
                    continue;
                }

                bool res = ProtocalCmd.WriteParam(index, value, SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, 1);

                if (res)
                {
                    MainForm.Logger.Info($"成功写入行[{i - 20}]：索引={index}, 值={value}");
                    bool resSave = ProtocalCmd.SaveParam(SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, 1);
                    if (resSave)
                        MainForm.Logger.Info("保存指令发送成功");
                    else
                        MainForm.Logger.Info("保存指令发送失败");
                }
                else
                {
                    MainForm.Logger.Error($"写入行[{i - 20}]失败：索引={index}, 值={value}");
                }
            }

        });

        
    }
    private string FormatFloatValue(float value)
    {
        // 基本格式化，保留12位小数
        string result = value.ToString("F12");

        // 处理小数点后多余的零
        if (result.Contains('.'))
        {
            result = result.TrimEnd('0');
            if (result.EndsWith("."))
            {
                result += "0";
            }
        }

        return result;
    }

    private void btn_Home_Click(object sender, EventArgs e)
    {
        //SingleForm.ShowForm<MainForm>();
        this.Hide();
        mainForm.Show();
    }
    private void PopFormTAL_FormClosed(object sender, FormClosedEventArgs e)
    {
        this.Hide();
        mainForm.Show();
        //SingleForm.ShowForm<MainForm>();
    }
}
