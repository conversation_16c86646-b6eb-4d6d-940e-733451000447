﻿namespace CCTS;

partial class PopFormParametersConfiguration

{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }


    #region Windows Form Designer generated code
    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// 2025-06-05
    /// </summary>
    private void InitializeComponent()
    {
        this.components = new System.ComponentModel.Container();
        this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
        this.ClientSize = new System.Drawing.Size(2280, 1100);
        this.Text = "Parameters Configuration";
        // 暂时移除图标设置以避免路径问题
        // this.Icon = new System.Drawing.Icon(@"CCTS.ico");
        this.StartPosition = FormStartPosition.CenterScreen;
        this.BackColor = Color.FromArgb(78, 149, 217);
        this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.PopFormParametersConfiguration_FormClosed);
        //
        //label1
        //
        this.label1 = new Label();
        this.label1.Text = "PARAMETERS CONFIGURATION";
        this.label1.Size = new Size(950, 100);
        this.label1.Anchor = AnchorStyles.None;
        this.label1.Location = new Point((this.ClientSize.Width - label1.Width) / 2, 60);
        this.label1.Font = new Font(label1.Font.FontFamily, 24, FontStyle.Bold);
        this.label1.ForeColor = Color.White;
        this.Controls.Add(label1);
        //
        //panel_Left
        //        
        this.panel_Left = new CCTS.RoundcornerPanel();
        this.panel_Left.BackColor = Color.FromArgb(11, 48, 65);
        this.panel_Left.ForeColor = Color.White;
        this.panel_Left.Size = new Size(1090, 750);
        this.panel_Left.Location = new Point(35, label1.Height + 80);
        this.Controls.Add(panel_Left);
        //
        //btn_Write
        //
        this.btn_Write = new CCTS.Roundcornerbtn();
        this.btn_Write.Text = "Write";
        this.btn_Write.Size = new Size(150, 80);
        this.btn_Write.Location = new Point(240, 630);
        this.btn_Write.ForeColor = Color.White;
        this.btn_Write.Font = new Font(this.btn_Write.Font.FontFamily, 12);
        this.btn_Write.BackColor = Color.FromArgb(47, 126, 39);//绿色
        this.btn_Write.FlatStyle = FlatStyle.Flat;
        this.btn_Write.FlatAppearance.BorderSize = 0;
        this.btn_Write.Click += new System.EventHandler(this.btn_Write_Click);
        this.panel_Left.Controls.Add(this.btn_Write);
        //
        //btn_ReadLeft
        //
        this.btn_ReadLeft = new CCTS.Roundcornerbtn();
        this.btn_ReadLeft.Text = "Read";
        this.btn_ReadLeft.Size = new Size(150, 80);
        this.btn_ReadLeft.Location = new Point(540, 630);
        this.btn_ReadLeft.ForeColor = Color.White;
        this.btn_ReadLeft.Font = new Font(this.btn_ReadLeft.Font.FontFamily, 12);
        this.btn_ReadLeft.BackColor =  Color.FromArgb(233, 113, 50);//黄色
        this.btn_ReadLeft.FlatStyle = FlatStyle.Flat;
        this.btn_ReadLeft.FlatAppearance.BorderSize = 0;
        this.btn_ReadLeft.Click += new System.EventHandler(this.btn_ReadLeft_Click);
        this.panel_Left.Controls.Add(this.btn_ReadLeft);
        #region
        //     Label label2 = new Label();
        //     label2.Text = "Max.Speed";
        //     label2.TextAlign = ContentAlignment.MiddleCenter;
        //     label2.Size = new Size(240, 40);
        //     label2.Location = new Point(5, 40);
        //     label2.Font = new Font(label2.Font.FontFamily, 10);
        //     label2.ForeColor = Color.White;
        //     label2.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label2);

        //     CenteredTextBox text_MaxSpeed = new CenteredTextBox();
        //     text_MaxSpeed.Multiline = true;
        //     text_MaxSpeed.Text = "4500RPM";
        //     text_MaxSpeed.SelectionAlignment = HorizontalAlignment.Center;
        //     //text_MaxSpeed.TextAlign = HorizontalAlignment.Center;
        //     text_MaxSpeed.Size = new Size(200, 45);
        //     text_MaxSpeed.BackColor = Color.White;
        //     text_MaxSpeed.ForeColor = Color.Black;
        //     text_MaxSpeed.Location = new Point(label2.Width + 5, 40);
        //     text_MaxSpeed.Font = new Font(text_MaxSpeed.Font.FontFamily, 10);
        //     text_MaxSpeed.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(text_MaxSpeed);

        //     Label label2_1 = new Label();
        //     label2_1.Text = "(Compressor maximum speed allowed)";
        //     label2_1.TextAlign = ContentAlignment.MiddleLeft;
        //     label2_1.Size = new Size(500, 40);
        //     label2_1.Location = new Point(label2.Width + text_MaxSpeed.Width + 5, 40);
        //     label2_1.Font = new Font(label2.Font.FontFamily, 10);
        //     label2_1.ForeColor = Color.White;
        //     label2_1.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label2_1);


        //     Label label3 = new Label();
        //     label3.Text = "Min.Speed";
        //     label3.TextAlign = ContentAlignment.MiddleCenter;
        //     label3.Size = new Size(240, 40);
        //     label3.Location = new Point(5, label2.Height + 55);
        //     label3.Font = new Font(label2.Font.FontFamily, 10);
        //     label3.ForeColor = Color.White;
        //     label3.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label3);

        //     CenteredTextBox text_MinSpeed = new CenteredTextBox();
        //     text_MinSpeed.Multiline = true;
        //     text_MinSpeed.Size = new Size(200, 45);
        //     text_MinSpeed.Text = "1600RPM";
        //     text_MinSpeed.SelectionAlignment = HorizontalAlignment.Center;
        //     //text_MinSpeed.TextAlign = HorizontalAlignment.Center;
        //     text_MinSpeed.BackColor = Color.White;
        //     text_MinSpeed.ForeColor = Color.Black;
        //     text_MinSpeed.Location = new Point(label3.Width + 5, label2.Height + 55);
        //     text_MinSpeed.Font = new Font(text_MinSpeed.Font.FontFamily, 10);
        //     text_MinSpeed.MaxLength = 4500;
        //     text_MinSpeed.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(text_MinSpeed);

        //     Label label3_1 = new Label();
        //     label3_1.Text = "(Compressor minimum speed allowed)";
        //     label3_1.TextAlign = ContentAlignment.MiddleLeft;
        //     label3_1.Size = new Size(500, 40);
        //     label3_1.Location = new Point(label3.Width + text_MaxSpeed.Width + 5, label2.Height + 55);
        //     label3_1.Font = new Font(label2.Font.FontFamily, 10);
        //     label3_1.ForeColor = Color.White;
        //     label3_1.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label3_1);

        //     Label label4 = new Label();
        //     label4.Text = "Overvoltage Trip";
        //     label4.TextAlign = ContentAlignment.MiddleCenter;
        //     label4.Size = new Size(240, 40);
        //     label4.Location = new Point(5, label2.Height + label3.Height + 70);
        //     label4.Font = new Font(label2.Font.FontFamily, 10);
        //     label4.ForeColor = Color.White;
        //     label4.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label4);

        //     CenteredTextBox text_Overvoltage1 = new CenteredTextBox();
        //     text_Overvoltage1.Multiline = true;
        //     text_Overvoltage1.Size = new Size(200, 45);
        //     text_Overvoltage1.Text = "260Vac";
        //     text_Overvoltage1.SelectionAlignment = HorizontalAlignment.Center;
        //     //text_Overvoltage1.TextAlign = HorizontalAlignment.Center;
        //     text_Overvoltage1.BackColor = Color.White;
        //     text_Overvoltage1.ForeColor = Color.Black;
        //     text_Overvoltage1.Location = new Point(label4.Width + 5, label2.Height + label3.Height + 70);
        //     text_Overvoltage1.Font = new Font(text_Overvoltage1.Font.FontFamily, 10);
        //     text_Overvoltage1.MaxLength = 4500;
        //     text_Overvoltage1.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(text_Overvoltage1);

        //     Label label4_1 = new Label();
        //     label4_1.Text = "(Inverter overvoltage protection - 220V range)";
        //     label4_1.TextAlign = ContentAlignment.MiddleLeft;
        //     label4_1.Size = new Size(560, 40);
        //     label4_1.Location = new Point(label4.Width + text_MaxSpeed.Width + 5, label2.Height + label3.Height + 70);
        //     label4_1.Font = new Font(label4_1.Font.FontFamily, 10);
        //     label4_1.ForeColor = Color.White;
        //     label4_1.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label4_1);

        //     Label label5 = new Label();
        //     label5.Text = "Undervoltage Trip";
        //     label5.TextAlign = ContentAlignment.MiddleCenter;
        //     label5.Size = new Size(240, 40);
        //     label5.Location = new Point(5, label2.Height + label3.Height + label4.Height + 85);
        //     label5.Font = new Font(label5.Font.FontFamily, 10);
        //     label5.ForeColor = Color.White;
        //     label5.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label5);

        //     CenteredTextBox text_Undervoltage1 = new CenteredTextBox();
        //     text_Undervoltage1.Multiline = true;
        //     text_Undervoltage1.Size = new Size(200, 45);
        //     text_Undervoltage1.Text = "100Vac";
        //     text_Undervoltage1.SelectionAlignment = HorizontalAlignment.Center;
        //     //text_Undervoltage1.TextAlign = HorizontalAlignment.Center;
        //     text_Undervoltage1.BackColor = Color.White;
        //     text_Undervoltage1.ForeColor = Color.Black;
        //     text_Undervoltage1.Location = new Point(label4.Width + 5, label2.Height + label3.Height + label4.Height + 85);
        //     text_Undervoltage1.Font = new Font(text_Undervoltage1.Font.FontFamily, 10);
        //     text_Undervoltage1.MaxLength = 4500;
        //     text_Undervoltage1.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(text_Undervoltage1);

        //     Label label5_1 = new Label();
        //     label5_1.Text = "(Inverter undervoltage protection -220V range)";
        //     label5_1.TextAlign = ContentAlignment.MiddleLeft;
        //     label5_1.Size = new Size(560, 40);
        //     label5_1.Location = new Point(label4.Width + text_MaxSpeed.Width + 5, label2.Height + label3.Height + label4.Height + 85);
        //     label5_1.Font = new Font(label5_1.Font.FontFamily, 10);
        //     label5_1.ForeColor = Color.White;
        //     label5_1.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label5_1);

        //     Label label6 = new Label();
        //     label6.Text = "Overvoltage Trip";
        //     label6.TextAlign = ContentAlignment.MiddleCenter;
        //     label6.Size = new Size(240, 40);
        //     label6.Location = new Point(5, label2.Height + label3.Height + label4.Height + label5.Height + 100);
        //     label6.Font = new Font(label6.Font.FontFamily, 10);
        //     label6.ForeColor = Color.White;
        //     label6.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label6);

        //     CenteredTextBox text_Overvoltage2 = new CenteredTextBox();
        //     text_Overvoltage2.Multiline = true;
        //     text_Overvoltage2.Size = new Size(200, 45);
        //     text_Overvoltage2.Text = "260Vac";
        //     text_Overvoltage2.SelectionAlignment = HorizontalAlignment.Center;
        //     //text_Overvoltage2.TextAlign = HorizontalAlignment.Center;
        //     text_Overvoltage2.BackColor = Color.White;
        //     text_Overvoltage2.ForeColor = Color.Black;
        //     text_Overvoltage2.Location = new Point(label4.Width + 5, label2.Height + label3.Height + label4.Height + label5.Height + 100);
        //     text_Overvoltage2.Font = new Font(text_Overvoltage2.Font.FontFamily, 10);
        //     text_Overvoltage2.MaxLength = 4500;
        //     text_Overvoltage2.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(text_Overvoltage2);

        //     Label label6_1 = new Label();
        //     label6_1.Text = "(Inverter overvoltage protection -127V range)";
        //     label6_1.TextAlign = ContentAlignment.MiddleLeft;
        //     label6_1.Size = new Size(540, 40);
        //     label6_1.Location = new Point(label4.Width + text_MaxSpeed.Width + 5,
        //                                         label2.Height + label3.Height + label4.Height + label5.Height + 100);
        //     label6_1.Font = new Font(label6_1.Font.FontFamily, 10);
        //     label6_1.ForeColor = Color.White;
        //     label6_1.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label6_1);

        //     Label label7 = new Label();
        //     label7.Text = "Undervoltage Trip";
        //     label7.TextAlign = ContentAlignment.MiddleCenter;
        //     label7.Size = new Size(240, 40);
        //     label7.Location = new Point(5, label2.Height + label3.Height + label4.Height + label5.Height + label6.Height + 115);
        //     label7.Font = new Font(label7.Font.FontFamily, 10);
        //     label7.ForeColor = Color.White;
        //     label7.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label7);

        //     CenteredTextBox text_Undervoltage2 = new CenteredTextBox();
        //     text_Undervoltage2.Multiline = true;
        //     text_Undervoltage2.Size = new Size(200, 45);
        //     text_Undervoltage2.Text = "100Vac";
        //     text_Undervoltage2.SelectionAlignment = HorizontalAlignment.Center;
        //     //text_Undervoltage2.TextAlign = HorizontalAlignment.Center;
        //     text_Undervoltage2.BackColor = Color.White;
        //     text_Undervoltage2.ForeColor = Color.Black;
        //     text_Undervoltage2.Location = new Point(label4.Width + 5,
        //                                     label2.Height + label3.Height + label4.Height + label5.Height + label6.Height + 115);
        //     text_Undervoltage2.Font = new Font(text_Undervoltage2.Font.FontFamily, 10);
        //     text_Undervoltage2.MaxLength = 4500;
        //     text_Undervoltage2.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(text_Undervoltage2);

        //     Label label7_1 = new Label();
        //     label7_1.Text = "(Inverter overvoltage protection -127V range)";
        //     label7_1.TextAlign = ContentAlignment.MiddleLeft;
        //     label7_1.Size = new Size(540, 40);
        //     label7_1.Location = new Point(label4.Width + text_MaxSpeed.Width + 5,
        //                                         label2.Height + label3.Height + label4.Height + label5.Height + label6.Height + 115);
        //     label7_1.Font = new Font(label7_1.Font.FontFamily, 10);
        //     label7_1.ForeColor = Color.White;
        //     label7_1.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label7_1);

        //     Label label8 = new Label();
        //     label8.Text = "Power Limit";
        //     label8.TextAlign = ContentAlignment.MiddleCenter;
        //     label8.Size = new Size(240, 40);
        //     label8.Location = new Point(5, label2.Height + label3.Height + label4.Height +
        //                                                 label5.Height + label6.Height + label7.Height + 130);
        //     label8.Font = new Font(label8.Font.FontFamily, 10);
        //     label8.ForeColor = Color.White;
        //     label8.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label8);

        //     CenteredTextBox text_PowerLimit = new CenteredTextBox();
        //     text_PowerLimit.Multiline = true;
        //     text_PowerLimit.Size = new Size(200, 45);
        //     text_PowerLimit.Text = "160W";
        //     text_PowerLimit.SelectionAlignment = HorizontalAlignment.Center;
        //     //text_PowerLimit.TextAlign = HorizontalAlignment.Center;
        //     text_PowerLimit.BackColor = Color.White;
        //     text_PowerLimit.ForeColor = Color.Black;
        //     text_PowerLimit.Location = new Point(label4.Width + 5, label2.Height + label3.Height + label4.Height +
        //                                                                 label5.Height + label6.Height + label7.Height + 130);
        //     text_PowerLimit.Font = new Font(text_PowerLimit.Font.FontFamily, 10);
        //     text_PowerLimit.MaxLength = 4500;
        //     text_PowerLimit.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(text_PowerLimit);

        //     Label label8_1 = new Label();
        //     label8_1.Text = "(Inverter maximum output power)";
        //     label8_1.TextAlign = ContentAlignment.MiddleLeft;
        //     label8_1.Size = new Size(540, 40);
        //     label8_1.Location = new Point(label4.Width + text_MaxSpeed.Width + 5, label2.Height + label3.Height + label4.Height +
        //                                                                 label5.Height + label6.Height + label7.Height + 130);
        //     label8_1.Font = new Font(label8_1.Font.FontFamily, 10);
        //     label8_1.ForeColor = Color.White;
        //     label8_1.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label8_1);

        //     Label label9 = new Label();
        //     label9.Text = "Speed\nCommand";
        //     label9.TextAlign = ContentAlignment.MiddleCenter;
        //     label9.Size = new Size(240, 60);
        //     label9.Location = new Point(5, label2.Height + label3.Height + label4.Height +
        //                                                 label5.Height + label6.Height + label7.Height + label8.Height + 145);
        //     label9.Font = new Font(label9.Font.FontFamily, 10);
        //     label9.ForeColor = Color.White;
        //     label9.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label9);

        //     CenteredTextBox text_TAL = new CenteredTextBox();
        //     text_TAL.Multiline = true;
        //     text_TAL.Size = new Size(45, 55);
        //     text_TAL.Text = "";
        //     text_TAL.SelectionAlignment = HorizontalAlignment.Center;
        //     //text_TAL.TextAlign = HorizontalAlignment.Center;
        //     text_TAL.BackColor = Color.White;
        //     text_TAL.ForeColor = Color.Black;
        //     text_TAL.Location = new Point(label4.Width + 5, label2.Height + label3.Height + label4.Height +
        //                                                 label5.Height + label6.Height + label7.Height + label8.Height + 145);
        //     text_TAL.Font = new Font(text_PowerLimit.Font.FontFamily, 10);
        //     text_TAL.MaxLength = 4500;
        //     text_TAL.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(text_TAL);

        //     Label label10 = new Label();
        //     label10.Text = "TAL";
        //     label10.TextAlign = ContentAlignment.MiddleCenter;
        //     label10.Size = new Size(60, 60);
        //     label10.Location = new Point(label4.Width + text_TAL.Width + 5, label2.Height + label3.Height + label4.Height +
        //                                                 label5.Height + label6.Height + label7.Height + label8.Height + 145);
        //     label10.Font = new Font(label10.Font.FontFamily, 10);
        //     label10.ForeColor = Color.White;
        //     label10.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label10);

        //     CenteredTextBox text_Frequency = new CenteredTextBox();
        //     text_Frequency.Multiline = true;
        //     text_Frequency.Size = new Size(45, 55);
        //     text_Frequency.Text = "";
        //     text_Frequency.SelectionAlignment = HorizontalAlignment.Center;
        //     //text_Frequency.TextAlign = HorizontalAlignment.Center;
        //     text_Frequency.BackColor = Color.White;
        //     text_Frequency.ForeColor = Color.Black;
        //     text_Frequency.Location = new Point(label4.Width + text_TAL.Width + label10.Width + 5, label2.Height + label3.Height + label4.Height +
        //                                                 label5.Height + label6.Height + label7.Height + label8.Height + 145);
        //     text_Frequency.Font = new Font(text_Frequency.Font.FontFamily, 10);
        //     text_Frequency.MaxLength = 4500;
        //     text_Frequency.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(text_Frequency);

        //     Label label11 = new Label();
        //     label11.Text = "Frequency";
        //     label11.TextAlign = ContentAlignment.MiddleCenter;
        //     label11.Size = new Size(150, 60);
        //     label11.Location = new Point(label4.Width + text_TAL.Width + label10.Width + text_Frequency.Width + 5,
        //             label2.Height + label3.Height + label4.Height + label5.Height + label6.Height + label7.Height + label8.Height + 145);
        //     label11.Font = new Font(label11.Font.FontFamily, 10);
        //     label11.ForeColor = Color.White;
        //     label11.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label11);

        //     TextBox text_Serial = new TextBox();
        //     text_Serial.Multiline = true;
        //     text_Serial.Size = new Size(45, 55);
        //     text_Serial.Text = "X";
        //    // text_Serial.SelectionAlignment = HorizontalAlignment.Center;
        //     text_Serial.TextAlign = HorizontalAlignment.Center;
        //     text_Serial.BackColor = Color.White;
        //     text_Serial.ForeColor = Color.Black;
        //     text_Serial.Location = new Point(label4.Width + text_TAL.Width + label10.Width + text_Frequency.Width + label11.Width + 5,
        //             label2.Height + label3.Height + label4.Height + label5.Height + label6.Height + label7.Height + label8.Height + 145);
        //     text_Serial.Font = new Font(text_Serial.Font.FontFamily, 10);
        //     text_Serial.MaxLength = 4500;
        //     text_Serial.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(text_Serial);

        //     // CenteredDataGridView text_Serial = new CenteredDataGridView();
        //     // text_Serial.Size = new Size(80, 80);
        //     // text_Serial.BackgroundColor = Color.White;
        //     // text_Serial.ForeColor = Color.Black;
        //     // text_Serial.Location = new Point(label4.Width + text_TAL.Width + label10.Width + text_Frequency.Width + label11.Width + 5,
        //     //         label2.Height + label3.Height + label4.Height + label5.Height + label6.Height + label7.Height + label8.Height + 145);
        //     // text_Serial.Font = new Font(text_Serial.Font.FontFamily, 10);
        //     // text_Serial.BorderStyle = BorderStyle.None;
        //     // text_Serial.EditMode = DataGridViewEditMode.EditOnEnter;
        //     // panel_Left.Controls.Add(text_Serial);

        //     Label label12 = new Label();
        //     label12.Text = "Serial";
        //     label12.TextAlign = ContentAlignment.MiddleCenter;
        //     label12.Size = new Size(60, 80);
        //     label12.Location = new Point(label4.Width + text_TAL.Width + label10.Width + text_Frequency.Width + label11.Width + text_Serial.Width + 5,
        //             label2.Height + label3.Height + label4.Height + label5.Height + label6.Height + label7.Height + label8.Height + 145);
        //     label12.Font = new Font(label12.Font.FontFamily, 10);
        //     label12.ForeColor = Color.White;
        //     label12.BorderStyle = BorderStyle.None;
        //     panel_Left.Controls.Add(label12);
        #endregion


        //
        //panel_Right
        //        
        this.panel_Right = new CCTS.RoundcornerPanel();
        this.panel_Right.BackColor = Color.FromArgb(11, 48, 65);
        this.panel_Right.ForeColor = Color.White;
        this.panel_Right.Size = new Size(1100, 750);
        this.panel_Right.Location = new Point(panel_Left.Width + 60, label1.Height + 80);
        this.Controls.Add(panel_Right);
        //
        //btn_ReadRight
        //
        this.btn_ReadRight = new CCTS.Roundcornerbtn();
        this.btn_ReadRight.Text = "Read";
        this.btn_ReadRight.Size = new Size(150, 80);
        this.btn_ReadRight.Location = new Point(425, 630);
        this.btn_ReadRight.ForeColor = Color.White;
        this.btn_ReadRight.Font = new Font(this.btn_ReadRight.Font.FontFamily, 12);
        this.btn_ReadRight.BackColor = Color.FromArgb(233, 113, 50);//黄色
        this.btn_ReadRight.FlatStyle = FlatStyle.Flat;
        this.btn_ReadRight.FlatAppearance.BorderSize = 0;
        this.btn_ReadRight.Click += new System.EventHandler(this.btn_ReadRight_Click);
        this.panel_Right.Controls.Add(this.btn_ReadRight);
        //
        //btn_Home
        //
        this.btn_Home = new CCTS.Roundcornerbtn();
        this.btn_Home.Text = "Home";
        this.btn_Home.Size = new Size(150, 80);
        this.btn_Home.Location = new Point(100, 970);
        this.btn_Home.ForeColor = Color.White;
        this.btn_Home.Font = new Font(this.btn_Home.Font.FontFamily, 12);
        this.btn_Home.BackColor = Color.FromArgb(11, 48, 65);
        this.btn_Home.FlatStyle = FlatStyle.Flat;
        this.btn_Home.FlatAppearance.BorderSize = 0;
        this.btn_Home.Click += new EventHandler(this.btn_Home_Click);
        this.Controls.Add(this.btn_Home);
    }

    #endregion
    private System.Windows.Forms.Label label1;
    private System.Windows.Forms.Panel panel_Right, panel_Left; 

    private System.Windows.Forms.Button btn_Write, btn_ReadRight, btn_ReadLeft,btn_Home;
    
}
