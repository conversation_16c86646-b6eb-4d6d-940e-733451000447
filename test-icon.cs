using System;
using System.Windows.Forms;
using System.Drawing;

namespace TestIcon
{
    public partial class TestForm : Form
    {
        public TestForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "Test Icon Loading";
            this.Size = new Size(400, 300);
            
            // 测试图标加载
            try
            {
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                Console.WriteLine("Assembly: " + assembly.FullName);
                
                // 列出所有嵌入资源
                string[] resourceNames = assembly.GetManifestResourceNames();
                Console.WriteLine("Available resources:");
                foreach (string name in resourceNames)
                {
                    Console.WriteLine("  " + name);
                }
                
                using (var stream = assembly.GetManifestResourceStream("CCTS.CCTS.ico"))
                {
                    if (stream != null)
                    {
                        this.Icon = new System.Drawing.Icon(stream);
                        Console.WriteLine("Icon loaded successfully");
                    }
                    else
                    {
                        Console.WriteLine("Icon stream is null");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error loading icon: " + ex.Message);
                Console.WriteLine("Stack trace: " + ex.StackTrace);
            }
        }
    }

    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TestForm());
        }
    }
}
