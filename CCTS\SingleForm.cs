using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CCTS
{

    public static class SingleForm
    {
        // 存储所有窗体的字典
        private static readonly Dictionary<Type, Form> _openForms = new Dictionary<Type, Form>();
        private static readonly object _lock = new object();

        /// <summary>
        /// 获取或创建窗体实例，确保每个窗体只有一个实例
        /// </summary>
        /// <typeparam name="T">窗体类型</typeparam>
        /// <returns>窗体实例</returns>
        public static T GetForm<T>() where T : Form, new()
        {
            Type formType = typeof(T);

            // 检查窗体是否已存在
            if (_openForms.ContainsKey(formType))
            {
                Form existingForm = _openForms[formType];

                // 如果窗体已被释放，则创建新实例
                if (existingForm.IsDisposed)
                {
                    _openForms.Remove(formType);
                    return CreateNewFormInstance<T>();
                }

                return (T)existingForm;
            }

            // 窗体不存在，创建新实例
            return CreateNewFormInstance<T>();
        }

        private static T CreateNewFormInstance<T>() where T : Form, new()
        {
            T form = new T();
            Type formType = typeof(T);

            // 注册窗体关闭事件
            form.FormClosed += (sender, e) => _openForms.Remove(formType);

            // 添加到打开的窗体字典
            _openForms.Add(formType, form);

            return form;
        }

        /// <summary>
        /// 显示窗体，如果已存在则激活
        /// </summary>
        /// <typeparam name="T">窗体类型</typeparam>
        public static void ShowForm<T>() where T : Form, new()
        {
            Form form = GetForm<T>();

            if (form.Visible)
            {
                // 如果窗体已显示，则激活
                form.BringToFront();
                form.Activate();
            }
            else
            {
                // 如果窗体未显示，则显示
                form.Show();
            }
        }
        public static void CloseAllForms()
        {
            lock (_lock)
            {
                foreach (var form in _openForms.Values)
                {
                    if (!form.IsDisposed)
                    {
                        form.FormClosed -= (sender, e) => _openForms.Remove(form.GetType());
                        form.Close();
                    }
                }
                _openForms.Clear();
            }
        }
    }
}