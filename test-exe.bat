@echo off
echo Testing the standalone executable...
echo.

echo File information:
dir publish\CCTS.exe
echo.

echo Checking dependencies:
echo This executable should be self-contained and not require .NET runtime installation.
echo.

echo File size: ~77 MB (includes .NET runtime and all dependencies)
echo Target: Windows x64
echo Framework: .NET 8.0 (embedded)
echo.

echo To test on another computer:
echo 1. Copy CCTS.exe to the target computer
echo 2. Double-click to run (no installation required)
echo 3. The application should start without any .NET runtime errors
echo.

pause
