using System.Globalization;

namespace CCTS;

public partial class PopFormParametersConfiguration : Form
{
    private MainForm mainForm;
    public DoubleBufferedDataGridView dgParamLeft = new DoubleBufferedDataGridView();
    public DoubleBufferedDataGridView dgParamRight = new DoubleBufferedDataGridView();
    public DoubleBufferedDataGridView dgParamSpeed = new DoubleBufferedDataGridView();

    public string[][] colText_Left =
{
        new string[] {"Max.Speed", "Min.Speed", "Overvoltage Trip", "Undervoltage Trip", "Overvoltage Trip", "Undervoltage Trip", "Power Limit"},
        new string[] {"RPM  (Compressor maximum speed allowed)",  "RPM  (Compressor minimum speed allowed)",
        "Vac  (Inverter overvoltage protection - 220V range)",  "Vac  (Inverter undervoltage protection -220V range)",
            "Vac  (Inverter overvoltage protection -127V range)", "Vac  (Inverter overvoltage protection -127V range)",
                 "W  (Inverter maximum output power)"
        }
    };
    public string[][] colText_Right =
    {
        new string[] {"Max.Current", "Restart Time", "Max.Voltage", "Min.Voltage", "Max.Voltage", "Min.Voltage"},
        new string[] {"Amps  (Inverter overvoltage current trip range)",  "Sec  (Time to inverter resume operation after a fault)",
        "Vac  (Inverter maximum voltage resume - 220V range)",  "Vac  (Inverter minimum voltage resume - 220V range)",
          "Vac  (Inverter maximum voltage resume - 220V range)", "Vac  (Inverter minimum voltage resume - 220V range)"
        }
    };
    public string[] colText_Speed = { "Speed\r\nCommand", "", "TAL", "", "Frequency", "", "Serial" };
    int[] colWidth = { 240, 200, 640 };
    int[] speWidth = { 160, 50, 60, 50, 150, 50, 90 };
    short index;
    const double SpecMin = 0.0;  // 规格最小值
    const double SpecMax = 500.0; // 规格最大值

    public PopFormParametersConfiguration(MainForm mainForm)
    {
        InitializeComponent();
        this.mainForm = mainForm;

        setGrid0();
        setGrid1();
        setGrid2();

    }

    //左表
    private void setGrid0()
    {
        //设置grid样式
        dgParamLeft.AllowUserToAddRows = false;
        dgParamLeft.AllowUserToOrderColumns = false;
        dgParamLeft.AllowUserToDeleteRows = false;
        dgParamLeft.AllowUserToResizeRows = false;
        dgParamLeft.AllowUserToResizeColumns = false;
        dgParamLeft.RowHeadersVisible = false;
        dgParamLeft.BorderStyle = BorderStyle.None;
        dgParamLeft.RowHeadersVisible = false;
        dgParamLeft.ColumnHeadersVisible = false;
        

        dgParamLeft.BackgroundColor = Color.FromArgb(11, 48, 65);
        dgParamLeft.GridColor = Color.FromArgb(11, 48, 65);
        dgParamLeft.Font = new Font(dgParamLeft.Font.FontFamily, 10);
        dgParamLeft.Height = 495;
        dgParamLeft.Width = 1085;
        dgParamLeft.Location = new Point(5, 40);
        dgParamLeft.DefaultCellStyle.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
        panel_Left.Controls.Add(dgParamLeft);
        // //添加回车键点击支持
        // dgParamLeft.KeyDown += (sender, e) =>
        // {
        //     if (e.KeyCode == Keys.Enter && dgParamLeft.IsCurrentCellInEditMode)
        //     {
        //         dgParamLeft.EndEdit();
        //         e.Handled = true; // 阻止默认行为
        //     }
        // };
        // this.MouseDown += (sender, e) =>
        // {
        //     if (dgParamLeft.IsCurrentCellInEditMode)
        //     {
        //         // 检查点击位置是否在当前编辑控件内
        //         var currentCell = dgParamLeft.CurrentCell;
        //         if (currentCell != null)
        //         {
        //             var cellRect = dgParamLeft.GetCellDisplayRectangle(
        //                 currentCell.ColumnIndex,
        //                 currentCell.RowIndex,
        //                 false);

        //             if (!cellRect.Contains(e.Location))
        //             {
        //                 dgParamLeft.EndEdit();
        //             }
        //         }
        //     }
        // };
        dgParamLeft.CellValidating += (sender, e) =>
        {
           // 只处理第2列（可编辑列）
           if (e.ColumnIndex != 1) return;

           // 获取当前行
           int row = e.RowIndex;

            // 检查输入是否为数字
            //    if (!float.TryParse(e.FormattedValue.ToString(), out float value))
            //    {
            //         //e.Cancel = true; // 阻止离开单元格
            //        // MessageBox.Show("请输入数字!");
            //         dgParamLeft.BeginEdit(true);               
            //    } 
            float.TryParse(e.FormattedValue.ToString(), out float value);       

           //检查每行的数值范围 ---
            bool valid = false;
            string errorMsg = "";

            // 按行判断范围
            if (row == 0)
            {
                valid = (value >= 3000.0 && value <= 4500.0);
                errorMsg = "Value must be between 3000.0 and 4500.0";
                dgParamLeft.BeginEdit(true);
            }
            else if (row == 1)
            {
                valid = (value >= 1600.0 && value <= 4500.0);
                errorMsg = "Value must be between 1600.0 and 4500.0";
                dgParamLeft.BeginEdit(true);
            }
            else if (row == 2)
            {
                valid = (value >= 240.0 && value <= SpecMax);
                errorMsg = $"Value must be between 240.0 and {SpecMax}.";
                dgParamLeft.BeginEdit(true);
            }
            else if (row == 3)
            {
                valid = (value >= SpecMin && value <= 210.0);
                errorMsg = $"Value must be between {SpecMin} and 210.0";
                dgParamLeft.BeginEdit(true);
            }
            else if (row == 4)
            {
                valid = (value >= 135.0 && value <= SpecMax);
                errorMsg = $"Value must be between 135.0 and {SpecMax}";
                dgParamLeft.BeginEdit(true);
            }
            else if (row == 5)
            {
                valid = (value >= SpecMin && value <= 100.0);
                errorMsg = $"Value must be between {SpecMin} and 100.0";
                dgParamLeft.BeginEdit(true);
            }
            else if (row == 6)
            {
                valid = (value >= 150.0 && value <= 210.0);
                errorMsg = $"Value must be between 150.0 and 210.0";
                dgParamLeft.BeginEdit(true);
            }

           // 显示错误提示
           if (!valid)
           {
               MessageBox.Show(errorMsg);
               //e.Cancel = true;
               dgParamLeft.BeginEdit(true);
           }

           // 检查特殊规则 
           if (row == 0 || row == 1 || row == 2 || row == 3 || row == 4 || row == 5) 
           {
               if (dgParamLeft.Rows[0].Cells[1].Value != null && dgParamLeft.Rows[1].Cells[1].Value != null)// 速度相关行
               {
                   float maxSpeed = float.Parse(dgParamLeft.Rows[0].Cells[1].Value.ToString());
                   float minSpeed = float.Parse(dgParamLeft.Rows[1].Cells[1].Value.ToString());

                    if (maxSpeed < minSpeed)
                    {
                        MessageBox.Show("Maximum speed must be ≥ minimum speed");
                        //e.Cancel = true;
                        dgParamLeft.BeginEdit(true);
                    }
               }          
           }
   
        }; 

        for (int i = 0; i < 3; i++)
        {
            DataGridViewColumn customColumn;
            customColumn = new DataGridViewTextBoxColumn();
            customColumn.Width = colWidth[i];
            if (i == 0 || i == 2)
                customColumn.ReadOnly = true;
            if (i == 2)
                customColumn.DefaultCellStyle.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            else if (i == 1)
                customColumn.ValueType = typeof(float);             

            dgParamLeft.Columns.Add(customColumn);
        }
        //添加行
        for (int i = 0; i < colText_Left[0].Length; i++)
        {
            // 添加新行
            int rowIndex = dgParamLeft.Rows.Add();
            dgParamLeft.Rows[rowIndex].Cells[0].Value = colText_Left[0][i];
            dgParamLeft.Rows[rowIndex].Cells[2].Value = colText_Left[1][i];
            dgParamLeft.Rows[rowIndex].Height = 70;
            dgParamLeft.Rows[rowIndex].Cells[1].Style.BackColor = Color.White;
            dgParamLeft.Rows[rowIndex].Cells[1].Style.ForeColor = Color.Black;
            //dgParamLeft.Rows[rowIndex].Cells[1].Style.Padding = new Padding(0, 10, 0, 10);

            dgParamLeft.Rows[i].DefaultCellStyle.BackColor = Color.FromArgb(11, 48, 65);
            dgParamLeft.Rows[rowIndex].DividerHeight = 15;

            // 为每行添加范围提示
            // string tip = "";
            // if (i == 0) tip = "允许范围: 3000.0~4500.0";
            // else if (i == 1) tip = "允许范围: 1600.0~4500.0";
            // else if (i == 2) tip = $"允许范围: 240.0~{SpecMax}";
            // else if (i == 3) tip = $"允许范围: {SpecMin}~210.0";
            // else if (i == 4) tip = $"允许范围: 135.0~{SpecMax}";
            // else if (i == 5) tip = $"允许范围: {SpecMin}~100.0";
            // else if (i == 6) tip = $"允许范围: 150.0~210.0";

            //dgParamLeft.Rows[i].Cells[2].Value += "\n" + tip;

        }

    }

    //右表
    private void setGrid1()
    {
        //设置grid样式
        dgParamRight.AllowUserToAddRows = false;
        dgParamRight.AllowUserToOrderColumns = false;
        dgParamRight.AllowUserToDeleteRows = false;
        dgParamRight.AllowUserToResizeRows = false;
        dgParamRight.AllowUserToResizeColumns = false;
        //dgParamRight.EnableHeadersVisualStyles = false;
        dgParamRight.RowHeadersVisible = false;
        dgParamRight.BorderStyle = BorderStyle.None;
        dgParamRight.RowHeadersVisible = false;
        dgParamRight.ColumnHeadersVisible = false;


        dgParamRight.BackgroundColor = Color.FromArgb(11, 48, 65);
        dgParamRight.GridColor = Color.FromArgb(11, 48, 65);
        dgParamRight.Font = new Font(dgParamRight.Font.FontFamily, 10);
        dgParamRight.Height = 500;
        dgParamRight.Width = 1340;
        dgParamRight.Location = new Point(5, 40);
        dgParamRight.DefaultCellStyle.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
        panel_Right.Controls.Add(dgParamRight);

        for (int i = 0; i < 3; i++)
        {
            DataGridViewColumn customColumn;
            customColumn = new DataGridViewTextBoxColumn();
            customColumn.Width = colWidth[i];
            customColumn.ReadOnly = true;
            if (i == 1)
                customColumn.ValueType = typeof(float);
            else if (i == 2)
                customColumn.DefaultCellStyle.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;

            dgParamRight.Columns.Add(customColumn);
        }
        //添加行
        for (int i = 0; i < colText_Right[0].Length; i++)
        {
            // 添加新行
            int rowIndex = dgParamRight.Rows.Add();
            dgParamRight.Rows[rowIndex].Cells[0].Value = colText_Right[0][i];
            dgParamRight.Rows[rowIndex].Cells[2].Value = colText_Right[1][i];
            dgParamRight.Rows[rowIndex].Height = 70;
            dgParamRight.Rows[rowIndex].Cells[1].Style.BackColor = Color.White;
            dgParamRight.Rows[rowIndex].Cells[1].Style.ForeColor = Color.Black;
            dgParamRight.Rows[rowIndex].Cells[1].Style.Padding = new Padding(0, 10, 0, 10);

            dgParamRight.Rows[i].DefaultCellStyle.BackColor = Color.FromArgb(11, 48, 65);
            dgParamRight.Rows[rowIndex].DividerHeight = 15;

        }
        
    }

    private void setGrid2()
    {
        //设置grid样式
        dgParamSpeed.AllowUserToAddRows = false;
        dgParamSpeed.AllowUserToOrderColumns = false;
        dgParamSpeed.AllowUserToDeleteRows = false;
        dgParamSpeed.AllowUserToResizeRows = false;
        dgParamSpeed.AllowUserToResizeColumns = false;
        dgParamSpeed.RowHeadersVisible = false;
        dgParamSpeed.BorderStyle = BorderStyle.None;
        dgParamSpeed.RowHeadersVisible = false;
        dgParamSpeed.ColumnHeadersVisible = false;
        dgParamSpeed.CurrentCell = null;

        dgParamSpeed.BackgroundColor = Color.FromArgb(11, 48, 65);
        dgParamSpeed.GridColor = Color.FromArgb(11, 48, 65);
        dgParamSpeed.Font = new Font(dgParamLeft.Font.FontFamily, 10);
        dgParamSpeed.Height = 70;
        dgParamSpeed.Width = 1000;
        dgParamSpeed.Location = new Point(15, dgParamLeft.Height + 35);
        dgParamSpeed.DefaultCellStyle.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
        panel_Left.Controls.Add(dgParamSpeed);

        for (int i = 0; i < 7; i++)
        {
            DataGridViewColumn customColumn;
            customColumn = new DataGridViewTextBoxColumn();
            customColumn.Width = speWidth[i];
            if (i == 0 || i == 2 || i == 4 || i == 6)
                customColumn.ReadOnly = true;
            if (i == 2 || i == 4 || i == 6)
                customColumn.DefaultCellStyle.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;

            dgParamSpeed.Columns.Add(customColumn);
        }
        //添加行      
        int rowIndex = dgParamSpeed.Rows.Add();

        // 将colText_Speed中的值依次填入单元格
        for (int i = 0; i < colText_Speed.Length; i++)
        {
            dgParamSpeed.Rows[rowIndex].Cells[i].Value = colText_Speed[i];
            // 特别设置第一列的换行
            if (i == 0)
            {
                dgParamSpeed.Rows[rowIndex].Cells[0].Style.WrapMode = DataGridViewTriState.True;
            }

            // 设置可编辑单元格的样式
            if (i == 1 || i == 3 || i == 5)
            {
                dgParamSpeed.Rows[rowIndex].Cells[i].Style.BackColor = Color.White;
                dgParamSpeed.Rows[rowIndex].Cells[i].Style.ForeColor = Color.Black;
                //dgParamSpeed.Rows[rowIndex].Cells[i].Style.Padding = new Padding(0, 10, 0, 10);
            }
            dgParamSpeed.Rows[rowIndex].DefaultCellStyle.BackColor = Color.FromArgb(11, 48, 65);
            dgParamSpeed.Rows[rowIndex].Height = 70;
        }      
    }

    private void btn_Write_Click(object sender, EventArgs e)
    {
        dgParamLeft.EndEdit(); // 强制提交所有编辑中的单元格    
        float maxSpeed = float.Parse(dgParamLeft.Rows[0].Cells[1].Value.ToString());
        float minSpeed = float.Parse(dgParamLeft.Rows[1].Cells[1].Value.ToString());
         if (maxSpeed < minSpeed)
        {
            MessageBox.Show("Maximum speed must be ≥ minimum speed");
            return;
        }

        Task.Run(() =>
        {
            if (dgParamLeft == null || dgParamSpeed == null || dgParamLeft.Rows.Count == 0 || dgParamSpeed.Rows.Count == 0)
            {
                MainForm.Logger.Warn("数据表格未初始化或位空");
                return;
            }
            for (int i = 0; i < 8; i++)
            {
                index = (short)i;
                float value = 0;
                if (i < 7)
                {
                    bool parseSuccess = float.TryParse(dgParamLeft.Rows[i].Cells[1].Value.ToString(), out value);
                    if (!parseSuccess)
                    {
                        MainForm.Logger.Error($"第{i}行数据转换失败：{dgParamLeft.Rows[i].Cells[1].Value}");
                        continue;
                    }

                    bool res = ProtocalCmd.WriteParam(index, value, SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, 1);

                    if (res)
                    {
                        MainForm.Logger.Info($"成功写入行[{i}]：索引={index}, 值={value}");
                        bool resSave = ProtocalCmd.SaveParam(SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, 1);
                        if (resSave)
                            MainForm.Logger.Info("保存指令发送成功");
                        else
                            MainForm.Logger.Info("保存指令发送失败");


                    }
                    else
                    {
                        MainForm.Logger.Error($"写入行[{i}]失败：索引={index}, 值={value}");
                    }
                }
                else
                {
                    bool isCell1Empty = string.IsNullOrWhiteSpace(dgParamSpeed.Rows[0].Cells[1].Value?.ToString());
                    bool isCell2Empty = string.IsNullOrWhiteSpace(dgParamSpeed.Rows[0].Cells[3].Value?.ToString());
                    bool isCell3Empty = string.IsNullOrWhiteSpace(dgParamSpeed.Rows[0].Cells[5].Value?.ToString());

                    if (!isCell1Empty)
                        value = 1;
                    else if (!isCell2Empty)
                        value = 2;
                    else if (!isCell3Empty)
                        value = 3;
                    bool res = ProtocalCmd.WriteParam(index, value, SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, 1);

                    if (res)
                    {
                        MainForm.Logger.Info($"成功写入行[{i}]：索引={index}, 值={value}");
                        bool resSave = ProtocalCmd.SaveParam(SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, 1);
                        if (resSave)
                            MainForm.Logger.Info("保存指令发送成功");
                        else
                            MainForm.Logger.Info("保存指令发送失败");
                    }
                    else
                    {
                        MainForm.Logger.Error($"写入行[{i}]失败：索引={index}, 值={value}");
                    }
                }
            }
        });

    }
    private void btn_ReadLeft_Click(object sender, EventArgs e)
    {
        Task.Run(() =>
        {
            for (int i = 0; i < 8; i++)
            {
                //float val = 0;
                index = (short)i;
                bool res = ProtocalCmd.ReadParam(index, SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, 1);
                if (res)
                {
                    if (i < 7)
                    {
                        dgParamLeft.Invoke((MethodInvoker)delegate
                        {
                            dgParamLeft.Rows[i].Cells[1].Value = FormatFloatValue(ProtocalCmd.val);
                        });
                    }
                    else
                    {
                        dgParamSpeed.Invoke((MethodInvoker)delegate
                        {
                            if ((Int32)ProtocalCmd.val == 1)
                            {
                                dgParamSpeed.Rows[0].Cells[1].Value = "X";
                                dgParamSpeed.Rows[0].Cells[3].Value = " ";
                                dgParamSpeed.Rows[0].Cells[5].Value = " ";
                            }
                            else if ((Int32)ProtocalCmd.val == 2)
                            {
                                dgParamSpeed.Rows[0].Cells[1].Value = " ";
                                dgParamSpeed.Rows[0].Cells[3].Value = "X";
                                dgParamSpeed.Rows[0].Cells[5].Value = " ";
                            }
                            else if ((Int32)ProtocalCmd.val == 3)
                            {
                                dgParamSpeed.Rows[0].Cells[1].Value = " ";
                                dgParamSpeed.Rows[0].Cells[3].Value = " ";
                                dgParamSpeed.Rows[0].Cells[5].Value = "X";
                            }
                        });
                    }
                }

            }
        });
    }
    private void btn_ReadRight_Click(object sender, EventArgs e)
    {
        Task.Run(() =>
        {
            for (int i = 8; i < 14; i++)
            {
                //float val = 0;
                index = (short)i;
                bool res = ProtocalCmd.ReadParam(index, SysConfig.bTargetLinked == SysConfig.TRG_LINKED_MASTER, 1);
                if (res)
                {
                    dgParamRight.Invoke((MethodInvoker)delegate
                    {
                        dgParamRight.Rows[i - 8].Cells[1].Value = FormatFloatValue(ProtocalCmd.val);
                    });
                }

            }
        });
    }
    private string FormatFloatValue(float value)
    {
        // 基本格式化，保留12位小数
        string result = value.ToString("F12");

        // 处理小数点后多余的零
        if (result.Contains('.'))
        {
            result = result.TrimEnd('0');
            if (result.EndsWith("."))
            {
                result += "0";
            }
        }

        return result;
    }
    //设置输入值范围
    public class RowConstraint
    {
        public double? MinValue { get; set; }  // 可空类型
        public double? MaxValue { get; set; }
        public bool UseSpecMin { get; set; }    // 是否使用规格最小值
        public bool UseSpecMax { get; set; }    // 是否使用规格最大值
    }

    private readonly RowConstraint[] _rowConstraints = new RowConstraint[]
    {
    // 第1行：3000.0~4500.0
    new RowConstraint { MinValue = 3000.0, MaxValue = 4500.0 },    
    // 第2行：1600.0~4500.0
    new RowConstraint { MinValue = 1600.0, MaxValue = 4500.0 },    
    // 第3行：240~MaxSpec
    new RowConstraint { MinValue = 240.0, MaxValue = 500.0 },    
    // 第4行：MinSpec~210.0
    new RowConstraint { MinValue = 0.0,MaxValue = 210.0 },
    // 第5行：135~MaxSpec
    new RowConstraint { MinValue = 135.0,MaxValue = 500.0 },
    // 第6行：MinSpec~210.0
    new RowConstraint { MinValue = 0.0,MaxValue = 100.0 },
    // 第7行：150~260
    new RowConstraint { MinValue = 135.0,MaxValue = 500.0 },
    };

    // 规格值（示例值，实际应从配置文件或数据库获取）
    private const double MinFromSpec = 150.0;  // 规格最小值
    private const double MaxFromSpec = 5000.0; // 规格最大值

    private void btn_Home_Click(object sender, EventArgs e)
    {
        //SingleForm.ShowForm<MainForm>();
        this.Hide();
        mainForm.Show();
    }
    private void PopFormParametersConfiguration_FormClosed(object sender, FormClosedEventArgs e)
    {
        this.Hide();
        //SingleForm.ShowForm<MainForm>();
        mainForm.Show();
    }
}
