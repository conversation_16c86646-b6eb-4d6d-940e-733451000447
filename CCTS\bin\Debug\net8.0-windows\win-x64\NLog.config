﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

  <!-- 定义目标（target），即日志输出的位置 -->
  <targets>
    <!-- 信息日志目标 -->
    <target name="infoFile" xsi:type="File"
            fileName="logs/info/${shortdate}.log"
            archiveFileName="logs/info/archive/{#}.log"
            archiveAboveSize="10485760"
            archiveNumbering="Sequence"
            maxArchiveFiles="10"
            layout="${longdate} ${uppercase:${level}} ${message}" />
    <!-- 错误日志目标 -->
    <target name="errorFile" xsi:type="File"
            fileName="logs/error/${shortdate}.log"
            archiveFileName="logs/error/archive/{#}.log"
            archiveAboveSize="10485760"
            archiveNumbering="Sequence"
            maxArchiveFiles="10"
            layout="${longdate} ${uppercase:${level}} ${message}" />
  </targets>

  <!-- 定义日志规则 -->
  <rules>
    <!-- 匹配所有日志级别为 Info 的日志，将其写入 infoFile 目标 -->
    <logger name="*" minlevel="Info" maxlevel="Info" writeTo="infoFile" />
    <!-- 匹配所有日志级别为 Error 及以上的日志，将其写入 errorFile 目标 -->
    <logger name="*" minlevel="Error" writeTo="errorFile" />
  </rules>
</nlog>