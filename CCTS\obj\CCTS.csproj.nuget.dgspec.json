{"format": 1, "restore": {"C:\\work\\C#\\Tecumseh\\CCTS\\CCTS.csproj": {}}, "projects": {"C:\\work\\C#\\Tecumseh\\CCTS\\CCTS.csproj": {"version": "2025.7.10.1", "restore": {"projectUniqueName": "C:\\work\\C#\\Tecumseh\\CCTS\\CCTS.csproj", "projectName": "CCTS", "projectPath": "C:\\work\\C#\\Tecumseh\\CCTS\\CCTS.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\work\\C#\\Tecumseh\\CCTS\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.5, )", "autoReferenced": true}, "NLog": {"target": "Package", "version": "[5.5.0, )", "versionCentrallyManaged": true}, "NLog.Extensions.Logging": {"target": "Package", "version": "[5.5.0, )", "versionCentrallyManaged": true}, "System.IO.Ports": {"target": "Package", "version": "[9.0.6, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"NLog": "5.5.0", "NLog.Extensions.Logging": "5.5.0", "System.IO.Ports": "9.0.6"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.5, 9.0.5]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}