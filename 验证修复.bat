@echo off
chcp 65001 >nul
echo ========================================
echo CCTS 独立可执行文件验证
echo ========================================
echo.

echo 检查文件是否存在...
if exist "publish\CCTS.exe" (
    echo ✓ CCTS.exe 文件存在
    
    echo.
    echo 文件信息:
    dir publish\CCTS.exe
    
    echo.
    echo ========================================
    echo 修复内容总结:
    echo ========================================
    echo ✓ 修复了硬编码图标路径问题
    echo ✓ 将图标设置为嵌入资源
    echo ✓ 添加了配置文件异常处理
    echo ✓ 使用 .NET 8.0 提高兼容性
    echo.
    echo 该文件现在应该可以在其他 Windows 电脑上正常运行
    echo 无需安装 .NET 运行时环境
    echo.
    echo ========================================
    echo 部署说明:
    echo ========================================
    echo 1. 将 publish\CCTS.exe 复制到目标电脑
    echo 2. 双击运行（首次运行可能需要几秒钟）
    echo 3. 如果被防病毒软件阻止，请添加到白名单
    echo.
    
) else (
    echo ✗ CCTS.exe 文件不存在
    echo 请先运行发布命令
)

echo.
pause
